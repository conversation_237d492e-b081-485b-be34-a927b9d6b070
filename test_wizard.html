<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        .wizard-step {
            display: none !important;
        }

        .wizard-step.active {
            display: block !important;
            animation: fadeIn 0.3s ease-in;
        }

        /* Ensure only first step is visible initially */
        .wizard-step[data-step="2"],
        .wizard-step[data-step="3"], 
        .wizard-step[data-step="4"],
        .wizard-step[data-step="5"],
        .wizard-step[data-step="6"],
        .wizard-step[data-step="7"] {
            display: none !important;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Property Setup Wizard Test</h1>
        
        <!-- Test Steps -->
        <div class="wizard-step active" data-step="1">
            <h2>Step 1: Basic Information</h2>
            <p>This is step 1</p>
        </div>
        
        <div class="wizard-step" data-step="2">
            <h2>Step 2: Airbnb Data</h2>
            <p>This is step 2</p>
        </div>
        
        <div class="wizard-step" data-step="3">
            <h2>Step 3: House Rules</h2>
            <p>This is step 3</p>
            <div id="house-rules-container"></div>
        </div>
        
        <!-- Navigation -->
        <div class="mt-4">
            <button type="button" class="btn btn-outline-secondary" id="prev-btn">Previous</button>
            <button type="button" class="btn btn-outline-primary" id="save-progress">Save Progress</button>
            <button type="button" class="btn btn-primary" id="next-btn">Next</button>
        </div>
        
        <!-- Console Output -->
        <div class="mt-4">
            <h3>Console Output:</h3>
            <div id="console-output" style="background: #f8f9fa; padding: 10px; font-family: monospace; height: 200px; overflow-y: scroll;"></div>
        </div>
    </div>

    <script>
        // Capture console.log for debugging
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('console-output');

        function addToConsole(type, ...args) {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            div.textContent = `[${type.toUpperCase()}] ${args.join(' ')}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('warn', ...args);
        };
    </script>

    <!-- Include the wizard JavaScript -->
    <script src="concierge/static/js/property-setup-wizard.js"></script>
</body>
</html> 