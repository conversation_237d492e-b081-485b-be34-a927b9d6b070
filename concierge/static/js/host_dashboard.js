/*
Handles host-specific dashboard interactions like properties, knowledge bases.
*/

// Add CSS to the page for contact badges
(function() {
    const style = document.createElement('style');
    style.textContent = `
        .contact-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin: 0.25rem;
            border-radius: 0.25rem;
            background-color: #e9ecef;
            font-size: 0.875rem;
        }
    `;
    document.head.appendChild(style);
})();

// --- Globals & Initial Checks ---
if (typeof webkitSpeechRecognition !== 'undefined' || typeof SpeechRecognition !== 'undefined') {
    console.log('Web Speech API supported.');
} else {
    console.log('Web Speech API not supported in this browser.');
}

// --- Properties Logic ---
function loadProperties() {
    console.log('Loading properties for host from session user');
    const propertiesContainer = document.getElementById('properties-list-container'); // Target container
    const loadingDiv = document.getElementById('loading-properties'); // Optional loading indicator
    const noPropertiesDiv = document.getElementById('no-properties'); // Optional 'no properties' message

    if (!propertiesContainer) {
        console.error('Properties container (#properties-list-container) not found.');
        return;
    }

    if (loadingDiv) loadingDiv.style.display = 'block';
    if (noPropertiesDiv) noPropertiesDiv.style.display = 'none';
    propertiesContainer.innerHTML = ''; // Clear previous content

    // Fetch properties from the API endpoint using session auth
    fetch(`/api/host/properties`, {
        credentials: 'same-origin'  // Include cookies in the request
    })
        .then(response => {
            if (!response.ok) {
                // Try to get error message from response body
                return response.json().then(err => {
                    throw new Error(err.error || `HTTP error! status: ${response.status}`);
                }).catch(() => {
                    // Fallback if response body is not JSON or empty
                    throw new Error(`HTTP error! status: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(properties => {
            if (loadingDiv) loadingDiv.style.display = 'none';
            if (!properties || properties.length === 0) {
                if (noPropertiesDiv) noPropertiesDiv.style.display = 'block';
                else propertiesContainer.innerHTML = '<p>No properties found.</p>';
            } else {
                renderProperties(properties, propertiesContainer);
            }
        })
        .catch(error => {
            console.error('Error loading properties:', error);
            if (loadingDiv) loadingDiv.style.display = 'none';
            propertiesContainer.innerHTML = `<div class="alert alert-danger">Error loading properties: ${error.message}</div>`;
        });
}

function renderProperties(properties, container) {
    container.innerHTML = ''; // Clear previous content
    const listGroup = document.createElement('div');
    listGroup.className = 'list-group';

    properties.forEach(property => {
        const listItem = document.createElement('a');
        listItem.href = `/properties/${property.id}/edit`;
        listItem.className = 'list-group-item list-group-item-action flex-column align-items-start';

        const dFlex = document.createElement('div');
        dFlex.className = 'd-flex w-100 justify-content-between';

        const heading = document.createElement('h5');
        heading.className = 'mb-1';
        heading.textContent = property.name || 'Unnamed Property';

        // Add a small element for creation date if available
        const createDate = property.createdAt ? new Date(property.createdAt).toLocaleDateString() : '';
        const smallDate = document.createElement('small');
        smallDate.textContent = createDate ? `Created: ${createDate}` : '';

        dFlex.appendChild(heading);
        dFlex.appendChild(smallDate);

        const address = document.createElement('p');
        address.className = 'mb-1';
        address.textContent = property.address || 'No address provided.';

        // Optionally add more details like description
        // const description = document.createElement('small');
        // description.textContent = property.description || '';

        listItem.appendChild(dFlex);
        listItem.appendChild(address);
        // listItem.appendChild(description);

        // --- Reservation Container (only if iCal URL exists) ---
        if (property.ical_url) {
            const reservationsContainer = document.createElement('div');
            reservationsContainer.id = `property-reservations-container-${property.id}`;
            reservationsContainer.setAttribute('data-property-id', property.id);
            reservationsContainer.innerHTML = '<div class="small text-muted">Loading reservations...</div>';
            listItem.appendChild(reservationsContainer);
            // Load reservations for this property
            loadPropertyReservations(property.id, reservationsContainer);
        }

        listGroup.appendChild(listItem);
    });

    container.appendChild(listGroup);
}

// --- Load and Render Reservations for a Property ---
function loadPropertyReservations(propertyId, container) {
    if (!container) {
        console.error(`Reservation container provided was null for property ${propertyId}`);
        return;
    }
    container.innerHTML = '<div class="small text-muted">Loading reservations...</div>'; // Set loading state
    console.log(`Fetching reservations for property ${propertyId}...`); // Log start

    fetch(`/api/properties/${propertyId}/reservations`, {
        credentials: 'same-origin'  // Include cookies in the request
    })
        .then(res => {
            console.log(`Response status for property ${propertyId}: ${res.status}`); // Log status
            if (!res.ok) {
                // Try to get text for more error info, then throw
                return res.text().then(text => {
                    console.error(`Error response body for ${propertyId}:`, text);
                    throw new Error(`Failed to load reservations (status ${res.status})`);
                });
            }
            // Try to parse JSON, explicitly catch parsing error
            return res.json().catch(parseError => {
                console.error(`JSON parsing error for property ${propertyId}:`, parseError);
                throw new Error('Failed to parse reservation data.'); // Throw a new error
            });
        })
        .then(data => {
            console.log(`Successfully fetched reservations for property ${propertyId}:`, data);
            if (!data || !data.reservations || data.reservations.length === 0) {
                container.innerHTML = '<div class="small">No reservations found for this property.</div>';
                return;
            }

            // Sort reservations by start date (from earliest to latest)
            data.reservations.sort((a, b) => {
                const dateA = a.startDate ? new Date(a.startDate) : new Date(0);
                const dateB = b.startDate ? new Date(b.startDate) : new Date(0);
                return dateA - dateB;
            });

            let html = '<div class="mt-2 mb-2"><strong>Current & Upcoming Reservations:</strong></div>';
            html += '<ul class="list-group">';

            const now = new Date();

            // Group reservations by status
            const activeReservations = [];
            const upcomingReservations = [];
            const pastReservations = [];

            data.reservations.forEach(res => {
                // Use centralized date utilities for consistent date handling
                const startDateStr = res.startDate;
                const endDateStr = res.endDate;
                
                if (startDateStr && endDateStr) {
                    if (window.DateUtils.isReservationActive(startDateStr, endDateStr)) {
                        activeReservations.push(res);
                    } else if (window.DateUtils.isReservationUpcoming(startDateStr)) {
                        upcomingReservations.push(res);
                    } else {
                        pastReservations.push(res);
                    }
                } else {
                    // If dates are missing, put in upcoming section
                    upcomingReservations.push(res);
                }
            });

            // Render each group
            const renderReservation = (res, isActive) => {
                // Format dates using centralized date utilities
                const startDateStr = window.DateUtils.formatDateForDisplay(res.startDate);
                const endDateStr = window.DateUtils.formatDateForDisplay(res.endDate);

                // Get status using centralized utilities
                const status = window.DateUtils.getReservationStatus(res.startDate, res.endDate);
                const statusBadge = `<span class="badge ${status.class}">${status.text}</span>`;

                // Generate contact information HTML
                let contactsHtml = '';
                let contactCount = 0;

                // Primary contact from Airbnb
                if (res.guestPhoneNumber) {
                    contactsHtml += `<div class="contact-badge">
                        <i class="fas fa-user-circle"></i> ${res.guestName || 'Guest'}: ${res.guestPhoneNumber}
                    </div>`;
                    contactCount++;
                } else if (res.guestPhoneLast4) {
                    contactsHtml += `<div class="small text-muted">Airbnb: ***-***-${res.guestPhoneLast4}</div>`;
                }

                // Additional contacts
                if (res.AdditionalContacts && res.AdditionalContacts.length > 0) {
                    res.AdditionalContacts.forEach(contact => {
                        contactsHtml += `<div class="contact-badge">
                            <i class="fas fa-user"></i> ${contact.name || 'Guest'}: ${contact.phone}
                        </div>`;
                        contactCount++;
                    });
                }

                return `<li class="list-group-item ${isActive ? 'list-group-item-success' : ''}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="d-flex align-items-center mb-1">
                                ${statusBadge}
                                <span class="ms-2">${startDateStr} to ${endDateStr}</span>
                            </div>
                            <div class="contact-info mt-1">
                                ${contactsHtml || '<span class="text-muted">No contact information</span>'}
                            </div>
                        </div>
                        <div>
                            <a href="/properties/${propertyId}/reservations" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> Manage Contacts
                            </a>
                        </div>
                    </div>
                </li>`;
            };

            // Add active reservations first
            if (activeReservations.length > 0) {
                html += '<div class="mt-2 mb-1"><b>Active</b></div>';
                activeReservations.forEach(res => {
                    html += renderReservation(res, true);
                });
            }

            // Add upcoming reservations next
            if (upcomingReservations.length > 0) {
                html += '<div class="mt-2 mb-1"><b>Upcoming</b></div>';
                upcomingReservations.forEach(res => {
                    html += renderReservation(res, false);
                });
            }

            // Add past reservations last (limited to 5)
            if (pastReservations.length > 0) {
                const displayCount = Math.min(pastReservations.length, 5); // Limit to 5 past reservations
                html += `<div class="mt-2 mb-1"><b>Past</b> (${displayCount} of ${pastReservations.length})</div>`;
                pastReservations.slice(0, displayCount).forEach(res => {
                    html += renderReservation(res, false);
                });
            }

            html += '</ul>';
            container.innerHTML = html;
        })
        .catch(err => {
            console.error(`Final catch block error for property ${propertyId}:`, err);
            container.innerHTML = `<div class="text-danger">Error loading reservations: ${err.message}</div>`;
        });
}

// --- Initialization ---
document.addEventListener('DOMContentLoaded', () => {
    console.log("Host Dashboard DOM loaded");
    // Load properties directly using session authentication
    loadProperties();
});

// Auth listener is still useful to handle login state in the UI
// But we don't rely on it for initial data loading
firebase.auth().onAuthStateChanged(user => {
    if (user) {
        console.log('Host dashboard: User authenticated in Firebase');
        user.getIdToken().then(idToken => {
            sessionStorage.setItem('firebaseIdToken', idToken);
        }).catch(error => {
            console.error("Error getting ID token: ", error);
        });
    } else {
        console.log('Host dashboard: User not authenticated in Firebase.');
        // We don't redirect since server-side @login_required should handle this
    }
});
