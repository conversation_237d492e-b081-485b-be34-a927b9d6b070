{% extends "base.html" %}

{% block title %}Host Dashboard{% endblock %}

{% block head %}
<style>
    .chat-container {
        height: 400px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #f9f9f9;
    }
    .chat-message {
        margin-bottom: 15px;
        padding: 10px;
        border-radius: 5px;
    }
    .user-message {
        background-color: #e3f2fd;
        margin-left: 20%;
        margin-right: 5px;
    }
    .ai-message {
        background-color: #f5f5f5;
        margin-right: 20%;
        margin-left: 5px;
    }
    .reservation-card {
        margin-bottom: 20px;
        transition: transform 0.3s;
    }
    .reservation-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .contact-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        margin: 0.25rem;
        border-radius: 0.25rem;
        background-color: #e9ecef;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header d-flex justify-content-between align-items-center p-3 bg-light border-bottom shadow-sm">
    <h2>Host Dashboard</h2>
    <div>
        <span>Welcome, {{ display_name or 'Host' }}!</span>
    </div>
    <div>
        <button onclick="logout()" class="btn btn-outline-danger me-2">Logout</button>
        <!-- Add host-specific actions here if needed -->
    </div>
</div>

<div class="container mt-4">
    <div class="row">
        <!-- User Profile Section -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Your Profile</h4>
                    <a href="{{ url_for('views.profile') }}" class="btn btn-light btn-sm">Edit Profile</a>
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> {{ display_name or 'Not set' }}</p>
                    <p><strong>Email:</strong> {{ email or 'Not set' }}</p>
                    <p><strong>Phone:</strong> {{ phone_number or 'Not set' }}</p>
                    <p><strong>User ID:</strong> {{ user_id }}</p>
                    <p><strong>Role:</strong> {{ user_role }}</p>
                </div>
            </div>
        </div>

        <!-- Properties Section -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Your Properties</h4>
                    <a href="{{ url_for('views.properties_list') }}" class="btn btn-light btn-sm">Manage Properties</a>
                </div>
                <div class="card-body">
                    <h5 class="card-title">Your Properties</h5>
                    <!-- Container for properties list -->
                    <div id="properties-list-container">
                        <!-- Loading state (optional) -->
                        <div id="loading-properties" style="display: none;">Loading properties...</div>
                        <!-- No properties state (optional) -->
                        <div id="no-properties" style="display: none;">You haven't added any properties yet. <a href="{{ url_for('views.property_new') }}">Add one now?</a></div>
                        <!-- Properties will be loaded here by JavaScript -->
                    </div>
                    <a href="{{ url_for('views.properties_list') }}" class="btn btn-secondary mt-3">Manage Properties</a>
                    <a href="{{ url_for('views.property_new') }}" class="btn btn-primary mt-3">Add New Property</a>
                    <a href="{{ url_for('views.property_setup_wizard') }}" class="btn btn-success mt-3 ms-2">
                        <i class="bi bi-magic me-2"></i>Setup New Property
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Use host-specific dashboard script -->
<script>
    // Debug output for template variables
    console.log("Host Dashboard Template Data:");
    console.log("- Name:", "{{ display_name }}" || "Not provided");
    console.log("- Email:", "{{ email }}" || "Not provided");
    console.log("- Phone Number:", "{{ phone_number }}" || "Not provided");
    console.log("- User ID:", "{{ user_id }}" || "Not available");
    console.log("- User Role:", "{{ user_role }}" || "Not available");
</script>
<script src="{{ url_for('static', filename='js/host_dashboard.js') }}"></script>
{% endblock %}
