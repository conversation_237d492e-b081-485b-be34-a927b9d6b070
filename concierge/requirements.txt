# Web framework
Flask==3.0.2
Flask-SocketIO==5.3.6
python-socketio==5.11.1
websockets>=13.0.0,<15.1.0

# Environment and utilities
python-dotenv==1.0.1
requests==2.31.0
icalendar==5.0.11
APScheduler==3.10.4
# resampy             # Removed
# numpy               # Removed
# pydub             # Removed - Not needed with OPUS
# pyaudioop         # Removed - Not needed
# For Opus audio decoding
# opuslib-python==0.0.3 # Incorrect package/version
opuslib==3.0.1
# Essential for numerical operations and ML
numpy>=1.24.3

# Firebase / Google Cloud
firebase-admin==6.4.0
google-cloud-firestore==2.20.2
google-cloud-storage==2.14.0
google-auth==2.27.0
google-auth-oauthlib==1.2.0
google-auth-httplib2==0.2.0
google-api-python-client==2.116.0

# Google AI SDKs
google-genai>=1.15.0  # New recommended SDK
google-generativeai==0.3.2  # Legacy SDK for backward compatibility

# AWS services (needed for some integrations)
boto3>=1.34.34
botocore>=1.34.34

# Machine Learning and Vector Database (optional but commonly used)
pandas>=2.0.0
scikit-learn>=1.0.0
lancedb>=0.20.0
chromadb>=1.0.0

# File Processing (for Knowledge Base)
pypdf==5.5.0
python-docx==1.1.0
openpyxl==3.1.2
python-magic==0.4.27
